# Supabase Setup Guide for Player Management App

## Step 1: Set up your Supabase Project

1. Go to [supabase.com](https://supabase.com) and sign in
2. Create a new project called "lab8"
3. Wait for the project to be fully provisioned

## Step 2: Get your Project Credentials

1. In your Supabase dashboard, go to **Settings** > **API**
2. Copy the following values:
   - **Project URL** (something like `https://your-project-id.supabase.co`)
   - **anon public key** (starts with `eyJ...`)

## Step 3: Update Environment Variables

1. Open the `.env.local` file in your project root
2. Replace the placeholder values with your actual Supabase credentials:

```env
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## Step 4: Create the Database Schema

1. In your Supabase dashboard, go to **SQL Editor**
2. Copy and paste the contents of `database-schema.sql` into the editor
3. Click **Run** to execute the SQL commands

This will create:
- A `players` table with all necessary columns
- Indexes for better performance
- Row Level Security policies
- Auto-updating timestamps
- Sample data (optional)

## Step 5: Test the Integration

1. Start your development server: `npm run dev`
2. Try creating a new player - it should save to Supabase
3. Check your Supabase dashboard under **Table Editor** > **players** to see the data
4. The player gallery should now load data from Supabase

## Database Schema

The `players` table has the following structure:

| Column | Type | Description |
|--------|------|-------------|
| id | BIGSERIAL | Primary key (auto-generated) |
| name | VARCHAR(255) | Player's full name |
| position | VARCHAR(50) | Player's position (Forward, Midfielder, etc.) |
| jersey_number | INTEGER | Player's jersey number |
| games_played | INTEGER | Number of games played (default: 0) |
| goals_scored | INTEGER | Number of goals scored (default: 0) |
| created_at | TIMESTAMP | When the record was created |
| updated_at | TIMESTAMP | When the record was last updated |

## Features Implemented

✅ **Create Player**: Add new players to the database
✅ **View Players**: Display all players from the database
✅ **Player Stats**: View individual player statistics
✅ **Loading States**: Show loading spinners during API calls
✅ **Error Handling**: Display error messages when operations fail
✅ **Real-time Data**: All data is fetched from Supabase

## Next Steps (To Be Implemented)

- Update Player functionality
- Delete Player functionality
- Search and filtering with database queries
- Player image uploads
- Advanced statistics tracking

## Troubleshooting

**Environment Variables Not Working?**
- Make sure your `.env.local` file is in the project root
- Restart your development server after updating environment variables
- Check that variable names start with `VITE_`

**Database Connection Issues?**
- Verify your Supabase URL and API key are correct
- Check that Row Level Security policies allow the operations
- Look at the browser console for detailed error messages

**No Data Showing?**
- Check the Supabase Table Editor to see if data exists
- Verify the table name is exactly `players`
- Check browser network tab for failed API requests
