import React from 'react';
import { useParams, useNavigate, <PERSON> } from 'react-router-dom';

function PlayerStats() {
  const { id } = useParams();
  const navigate = useNavigate();

  // Mock data - in a real app, this would come from your backend
  const mockPlayers = [
    {
      id: 1,
      name: '<PERSON>',
      position: 'Forward',
      team: 'Team A',
      jerseyNumber: 10,
      stats: { goals: 15, assists: 8, matches: 28, gamesPlayed: 28, goalsScored: 15 }
    },
    {
      id: 2,
      name: '<PERSON>',
      position: 'Midfielder',
      team: 'Team B',
      jerseyNumber: 8,
      stats: { goals: 5, assists: 12, matches: 25, gamesPlayed: 25, goalsScored: 5 }
    },
    {
      id: 3,
      name: '<PERSON>',
      position: 'Defender',
      team: 'Team A',
      jerseyNumber: 4,
      stats: { goals: 2, assists: 3, matches: 30, gamesPlayed: 30, goalsScored: 2 }
    },
    {
      id: 4,
      name: '<PERSON>',
      position: 'Goalkeeper',
      team: 'Team C',
      jerseyNumber: 1,
      stats: { cleanSheets: 12, saves: 89, matches: 26, gamesPlayed: 26, goalsScored: 0 }
    },
    {
      id: 5,
      name: '<PERSON>',
      position: 'Forward',
      team: 'Team B',
      jerseyNumber: 9,
      stats: { goals: 12, assists: 6, matches: 27, gamesPlayed: 27, goalsScored: 12 }
    },
    {
      id: 6,
      name: 'Emma Davis',
      position: 'Midfielder',
      team: 'Team C',
      jerseyNumber: 6,
      stats: { goals: 3, assists: 15, matches: 24, gamesPlayed: 24, goalsScored: 3 }
    }
  ];

  const player = mockPlayers.find(p => p.id === parseInt(id));

  if (!player) {
    return (
      <div className="error-container">
        <h2>Player not found</h2>
        <p>The player you're looking for doesn't exist.</p>
        <Link to="/player-gallery" className="btn btn-primary">
          Back to Gallery
        </Link>
      </div>
    );
  }

  return (
    <div className="player-stats-container">
      <div className="stats-header">
        <button onClick={() => navigate('/player-gallery')} className="back-btn">
          ← Back to Gallery
        </button>
        <h1>{player.name} - Statistics</h1>
        <div className="player-info">
          <span className="position-badge">{player.position}</span>
          <span className="jersey-number">#{player.jerseyNumber}</span>
          <span className="team-name">{player.team}</span>
        </div>
      </div>

      <div className="stats-content">
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-value">{player.stats.gamesPlayed || player.stats.matches}</div>
            <div className="stat-label">Games Played</div>
          </div>
          
          <div className="stat-card">
            <div className="stat-value">{player.stats.goalsScored || player.stats.goals}</div>
            <div className="stat-label">Goals Scored</div>
          </div>

          {player.position === 'Goalkeeper' ? (
            <>
              <div className="stat-card">
                <div className="stat-value">{player.stats.cleanSheets}</div>
                <div className="stat-label">Clean Sheets</div>
              </div>
              <div className="stat-card">
                <div className="stat-value">{player.stats.saves}</div>
                <div className="stat-label">Saves</div>
              </div>
            </>
          ) : (
            <div className="stat-card">
              <div className="stat-value">{player.stats.assists}</div>
              <div className="stat-label">Assists</div>
            </div>
          )}
        </div>

        <div className="stats-summary">
          <h3>Season Summary</h3>
          <p>
            {player.name} has played {player.stats.gamesPlayed || player.stats.matches} games this season
            {player.position !== 'Goalkeeper' && `, scoring ${player.stats.goalsScored || player.stats.goals} goals`}
            {player.position !== 'Goalkeeper' && player.stats.assists && ` and providing ${player.stats.assists} assists`}.
            {player.position === 'Goalkeeper' && ` with ${player.stats.cleanSheets} clean sheets and ${player.stats.saves} saves`}.
          </p>
        </div>
      </div>
    </div>
  );
}

export default PlayerStats;
