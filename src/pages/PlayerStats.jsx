import React from 'react';
import { useParams, useNavigate, <PERSON> } from 'react-router-dom';

function PlayerStats() {
  const { id } = useParams();
  const navigate = useNavigate();

  // Mock data - in a real app, this would come from your backend
  const mockPlayers = [];

  const player = mockPlayers.find(p => p.id === parseInt(id));

  if (!player) {
    return (
      <div className="error-container">
        <h2>Player not found</h2>
        <p>The player you're looking for doesn't exist.</p>
        <Link to="/player-gallery" className="btn btn-primary">
          Back to Gallery
        </Link>
      </div>
    );
  }

  return (
    <div className="player-stats-container">
      <div className="stats-header">
        <button onClick={() => navigate('/player-gallery')} className="back-btn">
          ← Back to Gallery
        </button>
        <h1>{player.name} - Statistics</h1>
        <div className="player-info">
          <span className="position-badge">{player.position}</span>
          <span className="jersey-number">#{player.jerseyNumber}</span>
          <span className="team-name">{player.team}</span>
        </div>
      </div>

      <div className="stats-content">
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-value">{player.stats.gamesPlayed || player.stats.matches}</div>
            <div className="stat-label">Games Played</div>
          </div>

          <div className="stat-card">
            <div className="stat-value">{player.stats.goalsScored || player.stats.goals}</div>
            <div className="stat-label">Goals Scored</div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default PlayerStats;
