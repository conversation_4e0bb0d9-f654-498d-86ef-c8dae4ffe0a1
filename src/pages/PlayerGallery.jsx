import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';

function PlayerGallery() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterPosition, setFilterPosition] = useState('');

  // Mock data - in a real app, this would come from your backend
  const mockPlayers = [
    {
      id: 1,
      name: '<PERSON>',
      position: 'Forward',
      team: 'Team A',
      jerseyNumber: 10,
      age: 25,
      stats: { goals: 15, matches: 28 }
    },
    {
      id: 2,
      name: '<PERSON>',
      position: 'Midfielder',
      team: 'Team B',
      jerseyNumber: 8,
      age: 23,
      stats: { goals: 5, matches: 25 }
    },
    {
      id: 3,
      name: '<PERSON>',
      position: 'Defender',
      team: 'Team A',
      jerseyNumber: 4,
      age: 28,
      stats: { goals: 2, matches: 30 }
    },
    {
      id: 4,
      name: '<PERSON>',
      position: 'Goalkeeper',
      team: 'Team C',
      jerseyNumber: 1,
      age: 26,
      stats: { cleanSheets: 12, saves: 89, matches: 26 }
    },
    {
      id: 5,
      name: '<PERSON>',
      position: 'Forward',
      team: 'Team B',
      jerseyNumber: 9,
      age: 24,
      stats: { goals: 12, assists: 6, matches: 27 }
    },
    {
      id: 6,
      name: '<PERSON>',
      position: 'Midfielder',
      team: 'Team C',
      jerseyNumber: 6,
      age: 22,
      stats: { goals: 3, assists: 15, matches: 24 }
    }
  ];

  const filteredPlayers = mockPlayers.filter(player => {
    const matchesSearch = player.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         player.team.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesPosition = filterPosition === '' || player.position === filterPosition;
    return matchesSearch && matchesPosition;
  });

  const getPositionColor = (position) => {
    switch (position) {
      case 'Forward': return '#ff6b6b';
      case 'Midfielder': return '#4ecdc4';
      case 'Defender': return '#45b7d1';
      case 'Goalkeeper': return '#96ceb4';
      default: return '#95a5a6';
    }
  };

  return (
    <div className="player-gallery-container">
      <div className="gallery-header">
        <h1>Player Gallery</h1>
        <p>Browse and manage your team roster</p>
      </div>

      <div className="gallery-controls">
        <div className="search-section">
          <input
            type="text"
            placeholder="Search players by name or team..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="search-input"
          />
        </div>

        <div className="filter-section">
          <select
            value={filterPosition}
            onChange={(e) => setFilterPosition(e.target.value)}
            className="filter-select"
          >
            <option value="">All Positions</option>
            <option value="Forward">Forward</option>
            <option value="Midfielder">Midfielder</option>
            <option value="Defender">Defender</option>
            <option value="Goalkeeper">Goalkeeper</option>
          </select>
        </div>

        <div className="add-player-section">
          <Link to="/create-player" className="btn btn-primary">
            + Add New Player
          </Link>
        </div>
      </div>

      <div className="players-grid">
        {filteredPlayers.length === 0 ? (
          <div className="no-players">
            <h3>No players found</h3>
            <p>Try adjusting your search or filters</p>
          </div>
        ) : (
          filteredPlayers.map(player => (
            <div key={player.id} className="player-card">
              <div className="player-card-header">
                <div className="player-number">#{player.jerseyNumber}</div>
                <div 
                  className="position-badge"
                  style={{ backgroundColor: getPositionColor(player.position) }}
                >
                  {player.position}
                </div>
              </div>
              
              <div className="player-card-body">
                <h3 className="player-name">{player.name}</h3>
                <p className="player-team">{player.team}</p>

                <div className="player-card-actions">
                  <Link to={`/player-stats/${player.id}`} className="btn btn-primary">
                    View Stats
                  </Link>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      <div className="gallery-summary">
        <p>Showing {filteredPlayers.length} of {mockPlayers.length} players</p>
      </div>
    </div>
  );
}

export default PlayerGallery; 