import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

function PlayerDetail() {
  const navigate = useNavigate();
  const [selectedPlayer, setSelectedPlayer] = useState('');
  const [playerData, setPlayerData] = useState(null);

  // Mock data - in a real app, this would come from your backend
  const mockPlayers = [];

  const handlePlayerSelect = (e) => {
    const playerId = e.target.value;
    setSelectedPlayer(playerId);

    if (playerId) {
      const player = mockPlayers.find(p => p.id === parseInt(playerId));
      setPlayerData(player);
    } else {
      setPlayerData(null);
    }
  };

  if (mockPlayers.length === 0) {
    return (
      <div className="error-container">
        <h2>No players</h2>
        <p>No players have been added yet. Start by creating your first player.</p>
        <div className="button-group">
          <button onClick={() => navigate('/create-player')} className="btn btn-primary">
            Create Player
          </button>
          <button onClick={() => navigate('/')} className="btn btn-secondary">
          Home
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="player-detail-container">
      <div className="form-header">
        <h1>Player Details</h1>
        <p>Select a player to view their information</p>
      </div>

      <div className="player-selector">
        <label htmlFor="player-select">Select Player to View:</label>
        <select
          id="player-select"
          value={selectedPlayer}
          onChange={handlePlayerSelect}
          className="player-select"
        >
          <option value="">Choose a player...</option>
          {mockPlayers.map(player => (
            <option key={player.id} value={player.id}>
              {player.name} - {player.position} (#{player.jerseyNumber})
            </option>
          ))}
        </select>
      </div>

      {playerData && (
        <div className="player-detail-content">
          <div className="player-header">
            <h2>{playerData.name}</h2>
            <div className="player-basic-info">
              <span className="position-badge">{playerData.position}</span>
              <span className="jersey-number">#{playerData.jerseyNumber}</span>
            </div>
          </div>

          <div className="player-info-section">
            <h3>Basic Information</h3>
            <div className="info-grid">
              <div className="info-item">
                <label>Position:</label>
                <span>{playerData.position}</span>
              </div>
              <div className="info-item">
                <label>Jersey Number:</label>
                <span>#{playerData.jerseyNumber}</span>
              </div>
            </div>
          </div>

          <div className="player-actions">
            <button onClick={() => navigate('/')} className="btn btn-secondary">
              Go Home
            </button>
            <button onClick={() => navigate('/update-player')} className="btn btn-primary">
              Edit Player
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default PlayerDetail; 