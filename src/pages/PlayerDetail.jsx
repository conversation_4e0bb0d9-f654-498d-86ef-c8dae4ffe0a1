import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

function PlayerDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [player, setPlayer] = useState(null);
  const [loading, setLoading] = useState(true);

  // Mock data - in a real app, this would come from your backend
  const mockPlayers = [
    {
      id: 1,
      name: '<PERSON>',
      position: 'Forward',
      team: 'Team A',
      jerseyNumber: 10,
      age: 25,
      height: 180,
      weight: 75,
      experience: 5,
      email: '<EMAIL>',
      phone: '******-0123',
      stats: {
        goals: 15,
        assists: 8,
        matches: 28,
        yellowCards: 2,
        redCards: 0
      },
      bio: '<PERSON> is a dynamic forward known for his speed and finishing ability. He joined the team in 2019 and has been a key player ever since.'
    },
    {
      id: 2,
      name: '<PERSON>',
      position: 'Midfielder',
      team: 'Team B',
      jerseyNumber: 8,
      age: 23,
      height: 165,
      weight: 60,
      experience: 3,
      email: '<EMAIL>',
      phone: '******-0124',
      stats: {
        goals: 5,
        assists: 12,
        matches: 25,
        yellowCards: 1,
        redCards: 0
      },
      bio: '<PERSON> is a creative midfielder with excellent vision and passing ability. She controls the tempo of the game and creates opportunities for her teammates.'
    },
    {
      id: 3,
      name: '<PERSON> <PERSON>',
      position: 'Defender',
      team: 'Team A',
      jersey<PERSON>umber: 4,
      age: 28,
      height: 185,
      weight: 80,
      experience: 7,
      email: '<EMAIL>',
      phone: '******-0125',
      stats: {
        goals: 2,
        assists: 3,
        matches: 30,
        yellowCards: 4,
        redCards: 0
      },
      bio: 'Mike is a solid defender with strong tackling and aerial ability. He provides leadership at the back and is known for his consistency.'
    },
    {
      id: 4,
      name: 'Sarah Wilson',
      position: 'Goalkeeper',
      team: 'Team C',
      jerseyNumber: 1,
      age: 26,
      height: 170,
      weight: 65,
      experience: 4,
      email: '<EMAIL>',
      phone: '******-0126',
      stats: {
        cleanSheets: 12,
        saves: 89,
        matches: 26,
        yellowCards: 0,
        redCards: 0
      },
      bio: 'Sarah is an agile goalkeeper with excellent reflexes and distribution skills. She has been instrumental in keeping clean sheets for her team.'
    }
  ];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      const foundPlayer = mockPlayers.find(p => p.id === parseInt(id));
      setPlayer(foundPlayer);
      setLoading(false);
    }, 500);
  }, [id]);

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading player details...</p>
      </div>
    );
  }

  if (!player) {
    return (
      <div className="error-container">
        <h2>Player Not Found</h2>
        <p>The player you're looking for doesn't exist.</p>
        <button onClick={() => navigate('/player-gallery')} className="btn btn-primary">
          Back to Gallery
        </button>
      </div>
    );
  }

  return (
    <div className="player-detail-container">
      <div className="player-header">
        <button onClick={() => navigate('/player-gallery')} className="btn btn-secondary back-btn">
          ← Back to Gallery
        </button>
        <h1>{player.name}</h1>
        <div className="player-basic-info">
          <span className="position-badge">{player.position}</span>
          <span className="team-badge">{player.team}</span>
          <span className="jersey-number">#{player.jerseyNumber}</span>
        </div>
      </div>

      <div className="player-content">
        <div className="player-info-section">
          <h2>Personal Information</h2>
          <div className="info-grid">
            <div className="info-item">
              <label>Age:</label>
              <span>{player.age} years old</span>
            </div>
            <div className="info-item">
              <label>Height:</label>
              <span>{player.height} cm</span>
            </div>
            <div className="info-item">
              <label>Weight:</label>
              <span>{player.weight} kg</span>
            </div>
            <div className="info-item">
              <label>Experience:</label>
              <span>{player.experience} years</span>
            </div>
            <div className="info-item">
              <label>Email:</label>
              <span>{player.email}</span>
            </div>
            <div className="info-item">
              <label>Phone:</label>
              <span>{player.phone}</span>
            </div>
          </div>
        </div>

        <div className="player-stats-section">
          <h2>Season Statistics</h2>
          <div className="stats-grid">
            {player.position === 'Goalkeeper' ? (
              <>
                <div className="stat-item">
                  <span className="stat-value">{player.stats.cleanSheets}</span>
                  <span className="stat-label">Clean Sheets</span>
                </div>
                <div className="stat-item">
                  <span className="stat-value">{player.stats.saves}</span>
                  <span className="stat-label">Saves</span>
                </div>
              </>
            ) : (
              <>
                <div className="stat-item">
                  <span className="stat-value">{player.stats.goals}</span>
                  <span className="stat-label">Goals</span>
                </div>
                <div className="stat-item">
                  <span className="stat-value">{player.stats.assists}</span>
                  <span className="stat-label">Assists</span>
                </div>
              </>
            )}
            <div className="stat-item">
              <span className="stat-value">{player.stats.matches}</span>
              <span className="stat-label">Matches</span>
            </div>
            <div className="stat-item">
              <span className="stat-value">{player.stats.yellowCards}</span>
              <span className="stat-label">Yellow Cards</span>
            </div>
            <div className="stat-item">
              <span className="stat-value">{player.stats.redCards}</span>
              <span className="stat-label">Red Cards</span>
            </div>
          </div>
        </div>

        <div className="player-bio-section">
          <h2>Biography</h2>
          <p>{player.bio}</p>
        </div>

        <div className="player-actions">
          <button onClick={() => navigate(`/update-player`)} className="btn btn-primary">
            Edit Player
          </button>
          <button className="btn btn-danger">
            Delete Player
          </button>
        </div>
      </div>
    </div>
  );
}

export default PlayerDetail; 