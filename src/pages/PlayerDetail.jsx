import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';

function PlayerDetail() {
  const { id } = useParams();
  const navigate = useNavigate();
  const [player, setPlayer] = useState(null);
  const [loading, setLoading] = useState(true);

  // Mock data - in a real app, this would come from your backend
  const mockPlayers = [];

  useEffect(() => {
    // Simulate API call
    setTimeout(() => {
      const foundPlayer = mockPlayers.find(p => p.id === parseInt(id));
      setPlayer(foundPlayer);
      setLoading(false);
    }, 500);
  }, [id]);

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading-spinner"></div>
        <p>Loading player details...</p>
      </div>
    );
  }

  if (!player) {
    return (
      <div className="error-container">
        <h2>Player Not Found</h2>
        <p>The player you're looking for doesn't exist.</p>
        <button onClick={() => navigate('/player-gallery')} className="btn btn-primary">
          Back to Gallery
        </button>
      </div>
    );
  }

  return (
    <div className="player-detail-container">
      <div className="player-header">
        <button onClick={() => navigate('/player-gallery')} className="btn btn-secondary back-btn">
          ← Back to Gallery
        </button>
        <h1>{player.name}</h1>
        <div className="player-basic-info">
          <span className="position-badge">{player.position}</span>
          <span className="team-badge">{player.team}</span>
          <span className="jersey-number">#{player.jerseyNumber}</span>
        </div>
      </div>

      <div className="player-content">
        <div className="player-info-section">
          <h2>Personal Information</h2>
          <div className="info-grid">
            <div className="info-item">
              <label>Age:</label>
              <span>{player.age} years old</span>
            </div>
            <div className="info-item">
              <label>Height:</label>
              <span>{player.height} cm</span>
            </div>
            <div className="info-item">
              <label>Weight:</label>
              <span>{player.weight} kg</span>
            </div>
            <div className="info-item">
              <label>Experience:</label>
              <span>{player.experience} years</span>
            </div>
            <div className="info-item">
              <label>Email:</label>
              <span>{player.email}</span>
            </div>
            <div className="info-item">
              <label>Phone:</label>
              <span>{player.phone}</span>
            </div>
          </div>
        </div>

        <div className="player-stats-section">
          <h2>Season Statistics</h2>
          <div className="stats-grid">
            {player.position === 'Goalkeeper' ? (
              <>
                <div className="stat-item">
                  <span className="stat-value">{player.stats.cleanSheets}</span>
                  <span className="stat-label">Clean Sheets</span>
                </div>
                <div className="stat-item">
                  <span className="stat-value">{player.stats.saves}</span>
                  <span className="stat-label">Saves</span>
                </div>
              </>
            ) : (
              <>
                <div className="stat-item">
                  <span className="stat-value">{player.stats.goals}</span>
                  <span className="stat-label">Goals</span>
                </div>
                <div className="stat-item">
                  <span className="stat-value">{player.stats.assists}</span>
                  <span className="stat-label">Assists</span>
                </div>
              </>
            )}
            <div className="stat-item">
              <span className="stat-value">{player.stats.matches}</span>
              <span className="stat-label">Matches</span>
            </div>
            <div className="stat-item">
              <span className="stat-value">{player.stats.yellowCards}</span>
              <span className="stat-label">Yellow Cards</span>
            </div>
            <div className="stat-item">
              <span className="stat-value">{player.stats.redCards}</span>
              <span className="stat-label">Red Cards</span>
            </div>
          </div>
        </div>

        <div className="player-bio-section">
          <h2>Biography</h2>
          <p>{player.bio}</p>
        </div>

        <div className="player-actions">
          <button onClick={() => navigate(`/update-player`)} className="btn btn-primary">
            Edit Player
          </button>
          <button className="btn btn-danger">
            Delete Player
          </button>
        </div>
      </div>
    </div>
  );
}

export default PlayerDetail; 