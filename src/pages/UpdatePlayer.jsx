import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

function UpdatePlayer() {
  const navigate = useNavigate();
  const [selectedPlayer, setSelectedPlayer] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    position: '',
    team: '',
    jerseyNumber: '',
    age: '',
    height: '',
    weight: '',
    experience: '',
    email: '',
    phone: ''
  });

  // Mock data - in a real app, this would come from your backend
  const mockPlayers = [
    { id: 1, name: '<PERSON>', position: 'Forward', team: 'Team A', jerseyNumber: 10 },
    { id: 2, name: '<PERSON>', position: 'Midfielder', team: 'Team B', jerseyNumber: 8 },
    { id: 3, name: '<PERSON>', position: 'Defender', team: 'Team A', jerseyNumber: 4 },
    { id: 4, name: '<PERSON>', position: 'Goalkeeper', team: 'Team C', jerseyNumber: 1 }
  ];

  const handlePlayerSelect = (e) => {
    const playerId = e.target.value;
    setSelectedPlayer(playerId);
    
    if (playerId) {
      const player = mockPlayers.find(p => p.id === parseInt(playerId));
      if (player) {
        setFormData({
          name: player.name,
          position: player.position,
          jerseyNumber: player.jerseyNumber.toString(),
        });
      }
    } else {
      setFormData({
        name: '',
        position: '',
        jerseyNumber: '',
      });
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Here you would typically update the data in your backend
    console.log('Updated player data:', formData);
    alert('Player updated successfully!');
    navigate('/player-gallery');
  };

  return (
    <div className="update-player-container">
      <div className="form-header">
        <h1>Update Player</h1>
        <p>Select a player and update their information</p>
      </div>
      
      <div className="player-selector">
        <label htmlFor="player-select">Select Player to Update:</label>
        <select
          id="player-select"
          value={selectedPlayer}
          onChange={handlePlayerSelect}
          className="player-select"
        >
          <option value="">Choose a player...</option>
          {mockPlayers.map(player => (
            <option key={player.id} value={player.id}>
              {player.name} - {player.position} (#{player.jerseyNumber})
            </option>
          ))}
        </select>
      </div>

      {selectedPlayer && (
        <form onSubmit={handleSubmit} className="player-form">
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="name">Full Name *</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="position">Position *</label>
              <select
                id="position"
                name="position"
                value={formData.position}
                onChange={handleChange}
                required
              >
                <option value="">Select Position</option>
                <option value="Forward">Forward</option>
                <option value="Midfielder">Midfielder</option>
                <option value="Defender">Defender</option>
                <option value="Goalkeeper">Goalkeeper</option>
              </select>
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="team">Team</label>
              <input
                type="text"
                id="team"
                name="team"
                value={formData.team}
                onChange={handleChange}
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="jerseyNumber">Jersey Number</label>
              <input
                type="number"
                id="jerseyNumber"
                name="jerseyNumber"
                value={formData.jerseyNumber}
                onChange={handleChange}
                min="1"
                max="99"
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="age">Age</label>
              <input
                type="number"
                id="age"
                name="age"
                value={formData.age}
                onChange={handleChange}
                min="16"
                max="50"
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="experience">Years of Experience</label>
              <input
                type="number"
                id="experience"
                name="experience"
                value={formData.experience}
                onChange={handleChange}
                min="0"
                max="20"
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="height">Height (cm)</label>
              <input
                type="number"
                id="height"
                name="height"
                value={formData.height}
                onChange={handleChange}
                min="150"
                max="220"
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="weight">Weight (kg)</label>
              <input
                type="number"
                id="weight"
                name="weight"
                value={formData.weight}
                onChange={handleChange}
                min="50"
                max="120"
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="email">Email</label>
              <input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="phone">Phone</label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
              />
            </div>
          </div>

          <div className="form-actions">
            <button type="button" onClick={() => navigate('/')} className="btn btn-secondary">
              Cancel
            </button>
            <button type="submit" className="btn btn-primary">
              Update Player
            </button>
          </div>
        </form>
      )}
    </div>
  );
}

export default UpdatePlayer; 