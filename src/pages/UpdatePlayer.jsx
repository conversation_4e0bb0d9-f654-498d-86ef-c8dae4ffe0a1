import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

function UpdatePlayer() {
  const navigate = useNavigate();
  const [selectedPlayer, setSelectedPlayer] = useState('');
  const [formData, setFormData] = useState({
    name: '',
    position: '',
    jerseyNumber: '',
    gamesPlayed: '',
    goalsScored: ''
  });

  // Mock data - in a real app, this would come from your backend
  const mockPlayers = [];

  const handlePlayerSelect = (e) => {
    const playerId = e.target.value;
    setSelectedPlayer(playerId);
    
    if (playerId) {
      const player = mockPlayers.find(p => p.id === parseInt(playerId));
      if (player) {
        setFormData({
          name: player.name,
          position: player.position,
          jerseyNumber: player.jerseyNumber.toString(),
          gamesPlayed: '',
          goalsScored: '',
        });
      }
    } else {
      setFormData({
        name: '',
        position: '',
        jerseyNumber: '',
        gamesPlayed: '',
        goalsScored: '',
      });
    }
  };

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    // Here you would typically update the data in your backend
    console.log('Updated player data:', formData);
    alert('Player updated successfully!');
    navigate('/player-gallery');
  };

  return (
    <div className="update-player-container">
      <div className="form-header">
        <h1>Update Player</h1>
        <p>Select a player and update their information</p>
      </div>
      
      <div className="player-selector">
        <label htmlFor="player-select">Select Player to Update:</label>
        <select
          id="player-select"
          value={selectedPlayer}
          onChange={handlePlayerSelect}
          className="player-select"
        >
          <option value="">Choose a player...</option>
          {mockPlayers.map(player => (
            <option key={player.id} value={player.id}>
              {player.name} - {player.position} (#{player.jerseyNumber})
            </option>
          ))}
        </select>
      </div>

      {selectedPlayer && (
        <form onSubmit={handleSubmit} className="player-form">
          <div className="form-row">
            <div className="form-group">
              <label htmlFor="name">Full Name *</label>
              <input
                type="text"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
              />
            </div>

            <div className="form-group">
              <label htmlFor="position">Position *</label>
              <select
                id="position"
                name="position"
                value={formData.position}
                onChange={handleChange}
                required
              >
                <option value="">Select Position</option>
                <option value="Forward">Forward</option>
                <option value="Midfielder">Midfielder</option>
                <option value="Defender">Defender</option>
                <option value="Goalkeeper">Goalkeeper</option>
              </select>
            </div>

            <div className="form-group">
              <label htmlFor="jerseyNumber">Jersey Number</label>
              <input
                type="number"
                id="jerseyNumber"
                name="jerseyNumber"
                value={formData.jerseyNumber}
                onChange={handleChange}
                min="1"
                max="99"
              />
            </div>
          </div>

          <div className="form-row">
            <div className="form-group">
              <label htmlFor="gamesPlayed">Games Played</label>
              <input
                type="number"
                id="gamesPlayed"
                name="gamesPlayed"
                value={formData.gamesPlayed}
                onChange={handleChange}
                min="0"
                max="100"
              />
            </div>

            <div className="form-group">
              <label htmlFor="goalsScored">Goals Scored</label>
              <input
                type="number"
                id="goalsScored"
                name="goalsScored"
                value={formData.goalsScored}
                onChange={handleChange}
                min="0"
                max="100"
              />
            </div>
          </div>
          <div className="form-actions">
            <button type="button" onClick={() => navigate('/')} className="btn btn-secondary">
              Cancel
            </button>
            <button type="submit" className="btn btn-primary">
              Update Player
            </button>
          </div>
        </form>
      )}
    </div>
  );
}

export default UpdatePlayer; 