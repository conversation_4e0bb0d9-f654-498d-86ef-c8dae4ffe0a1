-- Create the players table
CREATE TABLE players (
  id BIGSERIAL PRIMARY KEY,
  name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
  position VARCHAR(50) NOT NULL,
  jersey_number INTEGER,
  games_played INTEGER DEFAULT 0,
  goals_scored INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create an index on jersey_number for faster queries
CREATE INDEX idx_players_jersey_number ON players(jersey_number);

-- Create an index on position for filtering
CREATE INDEX idx_players_position ON players(position);

-- Enable Row Level Security (RLS)
ALTER TABLE players ENABLE ROW LEVEL SECURITY;

-- Create a policy that allows all operations for now (you can make this more restrictive later)
CREATE POLICY "Allow all operations on players" ON players
  FOR ALL USING (true);

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create a trigger to automatically update the updated_at column
CREATE TRIGGER update_players_updated_at 
  BEFORE UPDATE ON players 
  FOR EACH ROW 
  EXECUTE FUNCTION update_updated_at_column();

-- Insert some sample data (optional)
INSERT INTO players (name, position, jersey_number, games_played, goals_scored) VALUES
  ('John Doe', 'Forward', 10, 25, 15),
  ('Jane Smith', 'Midfielder', 8, 23, 5),
  ('Mike Johnson', 'Defender', 4, 28, 2),
  ('Sarah Wilson', 'Goalkeeper', 1, 26, 0);
